/* eslint-disable max-lines */
import {useEffect, useRef, useCallback, useMemo} from 'react';
import styled from '@emotion/styled';
import {Button, Modal} from '@panda-design/components';
import {Flex} from 'antd';
import {last} from 'lodash';
import {
    useMCPMessages,
    useMCPChatStatus,
    clearMCPMessages,
    setMCPConversationId,
    useMCPChat,
    useMCPMessage,
} from '@/regions/mcp/mcpPlaygroundChat';
import {IconDelete} from '@/icons/mcp';
import {
    ConversationIdProvider,
    useConversationId,
} from '@/components/Chat/Provider/ConversationIdProvider';
import bannerUrl from '@/assets/mcp/chatBg.png';
import {CONFIG_VALUES, RESPONSIVE_SPACING} from './constants';
import MessagePanel, {MessagePanelRef} from './MessagePanel';
import ChatMessageInput from './ChatMessageInput';
import {useChatMessage} from './hooks/useChatMessage';
import {AgentIdProvider} from './AgentIdProvider';

const Container = styled.div`
    height: 100vh;
    min-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MIN_WIDTH}px;
    background-image: url(${bannerUrl});
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
    overflow: hidden;
`;

const MessagesArea = styled.div`
    flex: 1;
    position: relative;
    overflow: hidden;
    padding-bottom: 140px;
`;

const InputWrapper = styled.div`
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
`;

const InputContainer = styled.div`
    width: 100%;
    box-sizing: border-box;
    padding: 0 100px;
    transition: padding 0.2s ease;

    @media (max-width: 1000px) {
        padding: 0 10px;
    }
`;

const ChatArea = () => {
    const conversationId = useConversationId();
    const messages = useMCPMessages();
    const status = useMCPChatStatus();
    const messagePanelRef = useRef<MessagePanelRef>(null);
    const {messageIds} = useMCPChat();
    const lastMessageId =
        messageIds && messageIds.length > 0 ? last(messageIds) : null;
    const lastMessage = useMCPMessage(lastMessageId);
    const lastElementContent = useMemo(
        () => {
            const elements = lastMessage?.elements;
            if (!elements || elements.length === 0) {
                return null;
            }
            return JSON.stringify(last(elements));
        },
        [lastMessage?.elements]
    );

    const handleScrollToBottom = useCallback(
        () => {
            messagePanelRef.current?.scrollToBottom();
        },
        []
    );
    useEffect(
        () => {
            if (lastElementContent) {
                handleScrollToBottom();
            }
        },
        [lastElementContent, handleScrollToBottom]
    );

    const {sendMessage, stopGeneration} =
        useChatMessage(handleScrollToBottom);

    const handleClearMessages = useCallback(
        () => {
            Modal.confirm({
                title: '清空对话',
                content: '确定要清空所有对话记录吗？此操作不可撤销。',
                onOk: () => {
                    clearMCPMessages();
                    setMCPConversationId('');
                    stopGeneration();
                },
            });
        },
        [stopGeneration]
    );

    const isDisabled = status === 'loading';

    const getDisabledReason = () => {
        if (status === 'loading') {
            return '正在处理消息...';
        }
        if (status === 'error') {
            return '发生错误，请稍后重试';
        }
        return '';
    };

    return (
        <AgentIdProvider agentId={CONFIG_VALUES.DEFAULT_AGENT_ID}>
            <ConversationIdProvider conversationId={conversationId}>
                <Container>
                    <Flex vertical style={{height: '100%'}}>
                        <MessagesArea>
                            <MessagePanel ref={messagePanelRef} show />
                        </MessagesArea>
                    </Flex>
                    <InputWrapper>
                        <Flex justify="center" style={{padding: '20px 0'}}>
                            <InputContainer>
                                <Flex vertical gap={4}>
                                    {messages.length > 0 && (
                                        <Flex justify="flex-end">
                                            <Button
                                                icon={<IconDelete />}
                                                style={{
                                                    border: 'none',
                                                    padding: '0',
                                                    backgroundColor: 'transparent',
                                                }}
                                                onClick={handleClearMessages}
                                            >
                                                清空对话
                                            </Button>
                                        </Flex>
                                    )}
                                    <ChatMessageInput
                                        disabled={isDisabled}
                                        disabledReason={getDisabledReason()}
                                        onSend={sendMessage}
                                        onStop={stopGeneration}
                                        isGenerating={status === 'loading'}
                                        placeholder="完成参数配置后对话"
                                    />
                                </Flex>
                            </InputContainer>
                        </Flex>
                    </InputWrapper>
                </Container>
            </ConversationIdProvider>
        </AgentIdProvider>
    );
};

export default ChatArea;
